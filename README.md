# نظام GuardianEye الأمني 🛡️

نظام مراقبة أمنية متقدم مكتوب بلغة Ring Programming Language

## المتطلبات
- Ring Programming Language
- مكتبة libui
- مكتبة RingThreadPro
- مكتبة SQLite
- مكتبة OpenSSL

## التشغيل

### الطريقة الأولى: استخدام الملف المساعد (مستحسن)
```bash
run.bat
```
سيعرض لك خيارات متعددة للتشغيل

### الطريقة الثانية: التشغيل المباشر
```bash
# النسخة المبسطة (مستقرة)
ring guardian_no_threads.ring

# النسخة التجريبية البسيطة
ring guardian_simple.ring

# النسخة الكاملة (قد تحتاج مكتبات إضافية)
ring main.ring
```

## الميزات الرئيسية

### 🔍 المراقبة الأمنية
- مسح الشبكة لاكتشاف الأجهزة
- فحص المنافذ المفتوحة
- تحليل ملفات السجلات
- مسح الثغرات الأمنية

### 🧵 نظام الخيوط المتعددة
- 5 خيوط عمل متوازية
- إدارة المهام بالأولوية
- حماية البيانات بـ Mutex

### 💾 قاعدة البيانات
- تخزين التهديدات المكتشفة
- تسجيل المهام والأنشطة
- تشفير البيانات الحساسة

### 📊 واجهة المستخدم
- واجهة رسومية سهلة الاستخدام
- عرض المهام النشطة
- جدول التهديدات المكتشفة
- تقارير مفصلة قابلة للتصدير

## كيفية الاستخدام

1. **بدء المراقبة**: اضغط على زر "بدء المراقبة"
2. **المسح الفوري**: استخدم زر "مسح فوري" لفحص هدف محدد
3. **عرض التقارير**: تحقق من لسان "التقارير" لرؤية النتائج
4. **إيقاف النظام**: اضغط على "إيقاف النظام" عند الانتهاء

## الملفات المهمة
- `main.ring` - الملف الرئيسي للتطبيق
- `security.db` - قاعدة بيانات SQLite للتخزين
- `run.bat` - ملف تشغيل مساعد

## الإصلاحات المطبقة
- ✅ إصلاح مكتبة التشفير (استخدام openssllib بدلاً من ringssl)
- ✅ إصلاح دوال SQLite (استخدام الصيغة الصحيحة)
- ✅ إصلاح استدعاء الدوال في واجهة المستخدم
- ✅ إضافة جداول قاعدة البيانات المفقودة
- ✅ تحسين معالجة الأخطاء

## ملاحظات
- التطبيق يستخدم محاكاة لبعض العمليات الأمنية
- يمكن تطوير التطبيق لاستخدام أدوات أمنية حقيقية
- البيانات محفوظة في قاعدة بيانات محلية مشفرة
