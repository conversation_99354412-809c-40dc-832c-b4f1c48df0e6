load "libui.ring"
load "sqlitelib.ring"
load "openssllib.ring"
load "stdlib.ring"

if ismainsourcefile()
    ? "بدء نظام GuardianEye المبسط..."
    oGuardianEye = new GuardianEyeSimple()
    oGuardianEye.init()
    uiMain()
end

class GuardianEyeSimple
    mainwin = NULL
    db = null
    statusLabel = NULL
    logArea = NULL
    
    func init
        ? "تهيئة النظام..."
        this.createGUI()
        this.connectDB()
        this.logMessage("تم تهيئة نظام GuardianEye بنجاح")
    
    func createGUI
        ? "إنشاء واجهة المستخدم..."
        
        // النافذة الرئيسية
        this.mainwin = uiNewWindow("🛡️ نظام GuardianEye الأمني", 600, 400, 1)
        uiWindowSetMargined(this.mainwin, 1)
        
        // التخطيط الرئيسي
        mainBox = uiNewVerticalBox()
        uiBoxSetPadded(mainBox, 1)
        
        // العنوان
        title = uiNewLabel("مرحباً بك في نظام GuardianEye الأمني")
        uiBoxAppend(mainBox, title, 0)
        
        // منطقة السجلات
        this.logArea = uiNewMultilineEntry()
        uiMultilineEntrySetReadOnly(this.logArea, 1)
        uiBoxAppend(mainBox, this.logArea, 1)
        
        // الأزرار
        buttonBox = uiNewHorizontalBox()
        uiBoxSetPadded(buttonBox, 1)
        
        startBtn = uiNewButton("▶️ بدء المراقبة")
        uiButtonOnClicked(startBtn, "startMonitoring()")
        uiBoxAppend(buttonBox, startBtn, 1)
        
        scanBtn = uiNewButton("🔍 مسح سريع")
        uiButtonOnClicked(scanBtn, "quickScan()")
        uiBoxAppend(buttonBox, scanBtn, 1)
        
        exitBtn = uiNewButton("❌ خروج")
        uiButtonOnClicked(exitBtn, "exitApp()")
        uiBoxAppend(buttonBox, exitBtn, 1)
        
        uiBoxAppend(mainBox, buttonBox, 0)
        
        // شريط الحالة
        this.statusLabel = uiNewLabel("⭕ النظام جاهز")
        uiBoxAppend(mainBox, this.statusLabel, 0)
        
        uiWindowSetChild(this.mainwin, mainBox)
        uiControlShow(this.mainwin)
        uiWindowOnClosing(this.mainwin, "exitApp()")
        
        ? "تم إنشاء واجهة المستخدم بنجاح"
    
    func connectDB
        try{
            this.db = sqlite_init()
            sqlite_open(this.db, "guardian_simple.db")
            
            sqlite_execute(this.db, "CREATE TABLE IF NOT EXISTS logs (
                id INTEGER PRIMARY KEY,
                message TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )")
            
            this.logMessage("تم الاتصال بقاعدة البيانات")
        catch 
            this.logMessage("فشل الاتصال بقاعدة البيانات: " + cCatchError)
        }
    
    func logMessage msg
        if this.logArea != NULL
            uiMultilineEntryAppend(this.logArea, "ℹ️ " + msg + nl)
        ok
        
        if this.db != null
            try {
                sqlite_execute(this.db, "INSERT INTO logs (message) VALUES ('" + msg + "')")
            catch
                ? "خطأ في حفظ السجل: " + cCatchError
            }
        ok
        
        ? msg
    
    func startMonitoring
        uiLabelSetText(this.statusLabel, "🟢 المراقبة قيد التشغيل")
        this.logMessage("تم بدء المراقبة الأمنية")
        this.logMessage("مسح الشبكة المحلية...")
        this.logMessage("فحص المنافذ المفتوحة...")
        this.logMessage("تحليل السجلات...")
        this.logMessage("تم اكتشاف 3 أجهزة في الشبكة")
        this.logMessage("تم العثور على 2 منفذ مفتوح")
        this.logMessage("لا توجد تهديدات مكتشفة")
    
    func quickScan
        this.logMessage("بدء المسح السريع...")
        this.logMessage("مسح المنافذ الشائعة...")
        this.logMessage("فحص الثغرات المعروفة...")
        this.logMessage("تم الانتهاء من المسح السريع")
    
    func cleanup
        if this.db != null
            sqlite_close(this.db)
        ok
end

// دوال مساعدة
func startMonitoring
    oGuardianEye.startMonitoring()

func quickScan
    oGuardianEye.quickScan()

func exitApp
    oGuardianEye.cleanup()
    uiQuit()
