load "libui.ring"
load "stdlib.ring"

? "اختبار واجهة المستخدم..."

if ismainsourcefile()
    ? "بدء اختبار libui..."
    
    try {
        ? "إنشاء نافذة بسيطة..."
        mainwin = uiNewWindow("اختبار", 400, 300, 1)
        ? "تم إنشاء النافذة"
        
        ? "إنشاء صندوق..."
        box = uiNewVerticalBox()
        ? "تم إنشاء الصندوق"
        
        ? "إنشاء تسمية..."
        label = uiNewLabel("مرحبا")
        ? "تم إنشاء التسمية"
        
        ? "إضافة التسمية للصندوق..."
        uiBoxAppend(box, label, 0)
        ? "تم إضافة التسمية"
        
        ? "ربط الصندوق بالنافذة..."
        uiWindowSetChild(mainwin, box)
        ? "تم ربط الصندوق"
        
        ? "عرض النافذة..."
        uiControlShow(mainwin)
        ? "تم عرض النافذة"
        
        ? "بدء الحلقة الرئيسية..."
        uiMain()
        
    catch
        ? "خطأ: " + cCatchError
    }
end
