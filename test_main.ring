load "libui.ring"
load "RingThreadPro.ring"
load "openssllib.ring"
load "sqlitelib.ring"
load "subprocess.ring"
load "ringregex.ring"
load "stdlib.ring"

? "تم تحميل جميع المكتبات بنجاح"

if ismainsourcefile()
    ? "بدء اختبار إنشاء الكائن..."
    
    try {
        oGuardianEye = new GuardianEyeSystem()
        ? "✅ تم إنشاء الكائن بنجاح"
        
        ? "اختبار تهيئة النظام..."
        oGuardianEye.init()
        ? "✅ تم تهيئة النظام بنجاح"
        
        ? "بدء واجهة المستخدم..."
        uiMain()
        
    catch
        ? "❌ خطأ: " + cCatchError
    }
end

class GuardianEyeSystem
    // إعدادات النظام
    poolSize = 5
    db = null
    isRunning = false
    
    // عناصر واجهة المستخدم
    mainwin = NULL
    tabs = NULL
    taskList = NULL
    threatTable = NULL
    reportArea = NULL
    statusLabel = NULL
    startButton = NULL
    stopButton = NULL
    scanButton = NULL
    
    // تهيئة النظام
    func init
        ? "بدء تهيئة النظام..."
        
        // إنشاء واجهة المستخدم
        ? "إنشاء واجهة المستخدم..."
        this.createGUI()
        
        // الاتصال بقاعدة البيانات
        ? "الاتصال بقاعدة البيانات..."
        this.connectDB()
        
        ? "تم تهيئة نظام GuardianEye بنجاح"
    
    func createGUI
        ? "إنشاء النافذة الرئيسية..."
        
        // النافذة الرئيسية
        this.mainwin = uiNewWindow("🛡️ نظام GuardianEye الأمني", 800, 600, 1)
        uiWindowSetMargined(this.mainwin, 1)
        
        // التخطيط الرئيسي
        mainBox = uiNewVerticalBox()
        uiBoxSetPadded(mainBox, 1)
        
        // إضافة تسمية بسيطة
        label = uiNewLabel("مرحباً بك في نظام GuardianEye الأمني")
        uiBoxAppend(mainBox, label, 0)
        
        // زر بسيط
        testButton = uiNewButton("اختبار")
        uiButtonOnClicked(testButton, func { 
            ? "تم الضغط على الزر!"
        })
        uiBoxAppend(mainBox, testButton, 0)
        
        uiWindowSetChild(this.mainwin, mainBox)
        uiControlShow(this.mainwin)
        uiWindowOnClosing(this.mainwin, func { 
            ? "إغلاق التطبيق..."
            uiQuit() 
        })
        
        ? "تم إنشاء واجهة المستخدم بنجاح"
    
    func connectDB
        try{
            ? "إنشاء قاعدة البيانات..."
            this.db = sqlite_init()
            sqlite_open(this.db, "security_test.db")
            
            sqlite_execute(this.db, "CREATE TABLE IF NOT EXISTS test (
                id INTEGER PRIMARY KEY,
                message TEXT
            )")
            
            sqlite_execute(this.db, "INSERT INTO test (message) VALUES ('اختبار قاعدة البيانات')")
            
            result = sqlite_execute(this.db, "SELECT * FROM test")
            ? "نتيجة الاختبار: " + result[1]["message"]
            
            ? "تم الاتصال بقاعدة البيانات بنجاح"
        catch 
            ? "فشل الاتصال بقاعدة البيانات: " + cCatchError
        }
end
