load "libui.ring"
load "openssllib.ring"
load "sqlitelib.ring"
load "stdlib.ring"

if ismainsourcefile()
    ? "بدء نظام GuardianEye (بدون خيوط)..."
    oGuardianEye = new GuardianEyeSystem()
    oGuardianEye.init()
    uiMain()
end

class GuardianEyeSystem
    db = null
    isRunning = false
    
    // عناصر واجهة المستخدم
    mainwin = NULL
    tabs = NULL
    taskList = NULL
    threatTable = NULL
    reportArea = NULL
    statusLabel = NULL
    startButton = NULL
    stopButton = NULL
    scanButton = NULL
    
    func init
        ? "تهيئة النظام..."
        this.createGUI()
        this.connectDB()
        this.logMessage("تم تهيئة نظام GuardianEye بنجاح")
    
    func createGUI
        ? "إنشاء واجهة المستخدم..."
        
        // النافذة الرئيسية
        this.mainwin = uiNewWindow("🛡️ نظام GuardianEye الأمني", 1000, 700, 1)
        uiWindowSetMargined(this.mainwin, 1)
        
        // التخطيط الرئيسي
        mainBox = uiNewVerticalBox()
        uiBoxSetPadded(mainBox, 1)
        
        // الألسنة
        this.tabs = uiNewTab()
        uiTabSetMargined(this.tabs, 0, 1)
        
        // لسان المهام
        taskTab = uiNewVerticalBox()
        uiBoxSetPadded(taskTab, 1)
        
        taskGroup = uiNewGroup("📋 المهام النشطة")
        uiGroupSetMargined(taskGroup, 1)
        taskGroupBox = uiNewVerticalBox()
        uiBoxSetPadded(taskGroupBox, 1)
        
        this.taskList = uiNewTable()
        uiTableAppendTextColumn(this.taskList, "المهمة", 0)
        uiTableAppendTextColumn(this.taskList, "الحالة", 1)
        uiTableAppendTextColumn(this.taskList, "الأولوية", 2)
        uiTableSetModel(this.taskList, uiNewTableModel(0, 3))
        
        uiBoxAppend(taskGroupBox, this.taskList, 1)
        uiGroupSetChild(taskGroup, taskGroupBox)
        uiBoxAppend(taskTab, taskGroup, 1)
        
        uiTabAppend(this.tabs, "المهام", taskTab)
        
        // لسان التهديدات
        threatTab = uiNewVerticalBox()
        uiBoxSetPadded(threatTab, 1)
        
        threatGroup = uiNewGroup("⚠️ التهديدات المكتشفة")
        uiGroupSetMargined(threatGroup, 1)
        threatGroupBox = uiNewVerticalBox()
        uiBoxSetPadded(threatGroupBox, 1)
        
        this.threatTable = uiNewTable()
        uiTableAppendTextColumn(this.threatTable, "نوع التهديد", 0)
        uiTableAppendTextColumn(this.threatTable, "المصدر", 1)
        uiTableAppendTextColumn(this.threatTable, "الأولوية", 2)
        uiTableAppendTextColumn(this.threatTable, "الوقت", 3)
        uiTableSetModel(this.threatTable, uiNewTableModel(0, 4))
        
        uiBoxAppend(threatGroupBox, this.threatTable, 1)
        uiGroupSetChild(threatGroup, threatGroupBox)
        uiBoxAppend(threatTab, threatGroup, 1)
        
        uiTabAppend(this.tabs, "التهديدات", threatTab)
        
        // لسان التقارير
        reportTab = uiNewVerticalBox()
        uiBoxSetPadded(reportTab, 1)
        
        reportGroup = uiNewGroup("📊 التقارير")
        uiGroupSetMargined(reportGroup, 1)
        reportGroupBox = uiNewVerticalBox()
        uiBoxSetPadded(reportGroupBox, 1)
        
        this.reportArea = uiNewMultilineEntry()
        uiMultilineEntrySetReadOnly(this.reportArea, 1)
        uiBoxAppend(reportGroupBox, this.reportArea, 1)
        
        exportBtn = uiNewButton("💾 تصدير التقرير")
        uiButtonOnClicked(exportBtn, "exportReportClicked()")
        uiBoxAppend(reportGroupBox, exportBtn, 0)
        
        uiGroupSetChild(reportGroup, reportGroupBox)
        uiBoxAppend(reportTab, reportGroup, 1)
        
        uiTabAppend(this.tabs, "التقارير", reportTab)
        
        // لوحة التحكم
        controlPanel = uiNewHorizontalBox()
        uiBoxSetPadded(controlPanel, 1)
        
        this.startButton = uiNewButton("▶️ بدء المراقبة")
        uiButtonOnClicked(this.startButton, "startSystemClicked()")
        
        this.stopButton = uiNewButton("⏹️ إيقاف النظام")
        uiButtonOnClicked(this.stopButton, "stopSystemClicked()")
        uiControlDisable(this.stopButton)
        
        this.scanButton = uiNewButton("🔍 مسح فوري")
        uiButtonOnClicked(this.scanButton, "quickScanClicked()")
        uiControlDisable(this.scanButton)
        
        uiBoxAppend(controlPanel, this.startButton, 1)
        uiBoxAppend(controlPanel, this.stopButton, 1)
        uiBoxAppend(controlPanel, this.scanButton, 1)
        
        // حالة النظام
        statusPanel = uiNewHorizontalBox()
        uiBoxSetPadded(statusPanel, 1)
        
        this.statusLabel = uiNewLabel("⭕ النظام متوقف")
        uiBoxAppend(statusPanel, this.statusLabel, 1)
        
        // تجميع الواجهة
        uiBoxAppend(mainBox, this.tabs, 1)
        uiBoxAppend(mainBox, controlPanel, 0)
        uiBoxAppend(mainBox, statusPanel, 0)
        
        uiWindowSetChild(this.mainwin, mainBox)
        uiControlShow(this.mainwin)
        uiWindowOnClosing(this.mainwin, "onClosingClicked()")
        
        ? "تم إنشاء واجهة المستخدم بنجاح"
    
    func connectDB
        try{
            this.db = sqlite_init()
            sqlite_open(this.db, "security.db")
            
            sqlite_execute(this.db, "CREATE TABLE IF NOT EXISTS threats (
                id INTEGER PRIMARY KEY,
                type TEXT,
                source TEXT,
                priority INTEGER,
                timestamp DATETIME,
                details TEXT
            )")
            
            sqlite_execute(this.db, "CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY,
                name TEXT,
                status TEXT,
                priority INTEGER,
                start_time DATETIME,
                end_time DATETIME
            )")
            
            this.logMessage("تم الاتصال بقاعدة البيانات")
        catch 
            this.logMessage("فشل الاتصال بقاعدة البيانات: " + cCatchError)
        }
    
    func startSystem
        this.isRunning = true
        this.logMessage("بدء المراقبة الأمنية")
        uiLabelSetText(this.statusLabel, "🟢 المراقبة قيد التشغيل")
        
        // تحديث حالة الأزرار
        uiControlDisable(this.startButton)
        uiControlEnable(this.stopButton)
        uiControlEnable(this.scanButton)
        
        // إضافة مهام تجريبية
        this.addTask("network_scan", "192.168.1.0/24")
        this.addTask("port_scan", "192.168.1.1")
        this.addTask("log_analysis", "/var/log/auth.log")
        
        this.logMessage("تم إضافة المهام الأولية")
    
    func quickScan
        this.logMessage("بدء المسح الفوري...")
        this.addTask("port_scan", "127.0.0.1")
        this.logMessage("تم إضافة مهمة المسح الفوري")
    
    func stopSystem
        this.isRunning = false
        this.logMessage("تم إيقاف النظام")
        uiLabelSetText(this.statusLabel, "🔴 النظام متوقف")
        
        // تحديث حالة الأزرار
        uiControlEnable(this.startButton)
        uiControlDisable(this.stopButton)
        uiControlDisable(this.scanButton)
        
        this.generateReport()
    
    func addTask taskType, target
        taskName = taskType + ": " + target
        priority = 5
        
        try {
            sqlite_execute(this.db,
                "INSERT INTO tasks (name, status, priority, start_time) 
                 VALUES ('" + taskName + "', 'معلقة', " + priority + ", datetime('now'))")
            
            // تحديث واجهة المهام
            model = uiTableModel(this.taskList)
            row = uiTableModelRowCount(model)
            uiTableModelInsertRow(model, row)
            uiTableModelSetCellValue(model, row, 0, taskName)
            uiTableModelSetCellValue(model, row, 1, "معلقة")
            uiTableModelSetCellValue(model, row, 2, string(priority))
            
            this.logMessage("تم إضافة مهمة: " + taskName)
        catch
            this.logMessage("خطأ في إضافة المهمة: " + cCatchError)
        }
    
    func generateReport
        report = "تقرير المراقبة الأمنية - " + date() + nl
        report += "================================" + nl + nl
        
        report += "المهام المضافة:" + nl
        tasks = sqlite_execute(this.db, "SELECT * FROM tasks")
        
        for task in tasks
            report += " - " + task['name'] + " (" + task['status'] + ")" + nl
        next
        
        report += nl + "انتهى التقرير" + nl
        
        // عرض التقرير
        uiMultilineEntrySetText(this.reportArea, report)
        this.logMessage("تم إنشاء التقرير")
    
    func exportReport
        report = uiMultilineEntryText(this.reportArea)
        filename = "security_report_" + date() + ".txt"
        try {
            file = fopen(filename, "w")
            fwrite(file, report)
            fclose(file)
            this.logMessage("تم تصدير التقرير إلى: " + filename)
        catch
            this.logMessage("فشل تصدير التقرير: " + cCatchError)
        }
    
    func logMessage msg
        if this.reportArea != NULL
            uiMultilineEntryAppend(this.reportArea, "ℹ️ " + msg + nl)
        ok
        ? msg
    
    func onClosing
        this.stopSystem()
        if this.db != null
            sqlite_close(this.db)
        ok
        uiQuit()
end

// دوال مساعدة لواجهة المستخدم
func exportReportClicked
    oGuardianEye.exportReport()

func startSystemClicked
    oGuardianEye.startSystem()

func stopSystemClicked
    oGuardianEye.stopSystem()

func quickScanClicked
    oGuardianEye.quickScan()

func onClosingClicked
    oGuardianEye.onClosing()
