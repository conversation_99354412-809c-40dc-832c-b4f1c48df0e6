load "libui.ring"

? "بدء اختبار libui..."

# إنشاء النافذة الرئيسية
oWindow = uiNewWindow("اختبار libui", 400, 300, 1)
uiWindowOnClosing(oWindow, "closeApp()")

# إنشاء صندوق عمودي
vbox = uiNewVerticalBox()
uiBoxSetPadded(vbox, 1)

# إضافة تسمية
label = uiNewLabel("مرحباً بك في اختبار libui!")
uiBoxAppend(vbox, label, 0)

# إضافة زر
button = uiNewButton("اضغط هنا")
uiButtonOnClicked(button, "buttonClicked()")
uiBoxAppend(vbox, button, 0)

# إضافة منطقة نص
entry = uiNewEntry()
uiEntrySetText(entry, "اكتب شيئاً هنا...")
uiBoxAppend(vbox, entry, 0)

# إضافة منطقة نص متعددة الأسطر
multitext = uiNewMultilineEntry()
uiMultilineEntrySetText(multitext, "هذا نص متعدد الأسطر" + nl + "يمكن كتابة عدة أسطر هنا")
uiBoxAppend(vbox, multitext, 1)

# إضافة زر الإغلاق
closeBtn = uiNewButton("إغلاق")
uiButtonOnClicked(closeBtn, "closeApp()")
uiBoxAppend(vbox, closeBtn, 0)

# ربط الصندوق بالنافذة
uiWindowSetChild(oWindow, vbox)

# عرض النافذة
uiControlShow(oWindow)

? "تم إنشاء النافذة، بدء الحلقة الرئيسية..."

# بدء الحلقة الرئيسية
uiMain()

func buttonClicked
    ? "تم الضغط على الزر!"
    uiMsgBox(oWindow, "رسالة", "تم الضغط على الزر بنجاح!")

func closeApp
    ? "إغلاق التطبيق..."
    uiQuit()
