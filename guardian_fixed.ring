load "libui.ring"
load "sqlitelib.ring"
load "openssllib.ring"
load "stdlib.ring"

# متغيرات عامة
oGuardianEye = NULL
mainwin = NULL
tabs = NULL
taskList = NULL
threatTable = NULL
reportArea = NULL
statusLabel = NULL
startButton = NULL
stopButton = NULL
scanButton = NULL
db = NULL
isRunning = false

if ismainsourcefile()
    ? "بدء نظام GuardianEye الأمني..."
    
    # إنشاء الكائن الرئيسي
    oGuardianEye = new GuardianEyeSystem()
    oGuardianEye.init()
    
    # بدء واجهة المستخدم
    uiMain()
end

class GuardianEyeSystem
    func init
        ? "تهيئة النظام..."
        this.connectDB()
        this.createGUI()
        this.logMessage("تم تهيئة نظام GuardianEye بنجاح")
    
    func createGUI
        ? "إنشاء واجهة المستخدم..."
        
        # النافذة الرئيسية
        mainwin = uiNewWindow("🛡️ نظام GuardianEye الأمني", 800, 600, 1)
        uiWindowSetMargined(mainwin, 1)
        uiWindowOnClosing(mainwin, "onClosingClicked()")
        
        # الصندوق الرئيسي
        mainBox = uiNewVerticalBox()
        uiBoxSetPadded(mainBox, 1)
        
        # الألسنة
        tabs = uiNewTab()
        uiTabSetMargined(tabs, 0, 1)
        
        # لسان المهام
        taskTab = uiNewVerticalBox()
        uiBoxSetPadded(taskTab, 1)
        
        taskGroup = uiNewGroup("📋 المهام النشطة")
        uiGroupSetMargined(taskGroup, 1)
        taskGroupBox = uiNewVerticalBox()
        uiBoxSetPadded(taskGroupBox, 1)
        
        # جدول المهام (مبسط)
        taskList = uiNewMultilineEntry()
        uiMultilineEntrySetReadOnly(taskList, 1)
        uiMultilineEntrySetText(taskList, "لا توجد مهام حالياً")
        
        uiBoxAppend(taskGroupBox, taskList, 1)
        uiGroupSetChild(taskGroup, taskGroupBox)
        uiBoxAppend(taskTab, taskGroup, 1)
        
        uiTabAppend(tabs, "المهام", taskTab)
        
        # لسان التهديدات
        threatTab = uiNewVerticalBox()
        uiBoxSetPadded(threatTab, 1)
        
        threatGroup = uiNewGroup("⚠️ التهديدات المكتشفة")
        uiGroupSetMargined(threatGroup, 1)
        threatGroupBox = uiNewVerticalBox()
        uiBoxSetPadded(threatGroupBox, 1)
        
        # جدول التهديدات (مبسط)
        threatTable = uiNewMultilineEntry()
        uiMultilineEntrySetReadOnly(threatTable, 1)
        uiMultilineEntrySetText(threatTable, "لا توجد تهديدات مكتشفة")
        
        uiBoxAppend(threatGroupBox, threatTable, 1)
        uiGroupSetChild(threatGroup, threatGroupBox)
        uiBoxAppend(threatTab, threatGroup, 1)
        
        uiTabAppend(tabs, "التهديدات", threatTab)
        
        # لسان التقارير
        reportTab = uiNewVerticalBox()
        uiBoxSetPadded(reportTab, 1)
        
        reportGroup = uiNewGroup("📊 التقارير")
        uiGroupSetMargined(reportGroup, 1)
        reportGroupBox = uiNewVerticalBox()
        uiBoxSetPadded(reportGroupBox, 1)
        
        reportArea = uiNewMultilineEntry()
        uiMultilineEntrySetReadOnly(reportArea, 1)
        uiBoxAppend(reportGroupBox, reportArea, 1)
        
        exportBtn = uiNewButton("💾 تصدير التقرير")
        uiButtonOnClicked(exportBtn, "exportReportClicked()")
        uiBoxAppend(reportGroupBox, exportBtn, 0)
        
        uiGroupSetChild(reportGroup, reportGroupBox)
        uiBoxAppend(reportTab, reportGroup, 1)
        
        uiTabAppend(tabs, "التقارير", reportTab)
        
        # لوحة التحكم
        controlPanel = uiNewHorizontalBox()
        uiBoxSetPadded(controlPanel, 1)
        
        startButton = uiNewButton("▶️ بدء المراقبة")
        uiButtonOnClicked(startButton, "startSystemClicked()")
        
        stopButton = uiNewButton("⏹️ إيقاف النظام")
        uiButtonOnClicked(stopButton, "stopSystemClicked()")
        uiControlDisable(stopButton)
        
        scanButton = uiNewButton("🔍 مسح فوري")
        uiButtonOnClicked(scanButton, "quickScanClicked()")
        uiControlDisable(scanButton)
        
        uiBoxAppend(controlPanel, startButton, 1)
        uiBoxAppend(controlPanel, stopButton, 1)
        uiBoxAppend(controlPanel, scanButton, 1)
        
        # شريط الحالة
        statusPanel = uiNewHorizontalBox()
        uiBoxSetPadded(statusPanel, 1)
        
        statusLabel = uiNewLabel("⭕ النظام متوقف")
        uiBoxAppend(statusPanel, statusLabel, 1)
        
        # تجميع الواجهة
        uiBoxAppend(mainBox, tabs, 1)
        uiBoxAppend(mainBox, controlPanel, 0)
        uiBoxAppend(mainBox, statusPanel, 0)
        
        uiWindowSetChild(mainwin, mainBox)
        uiControlShow(mainwin)
        
        ? "تم إنشاء واجهة المستخدم بنجاح"
    
    func connectDB
        try{
            db = sqlite_init()
            sqlite_open(db, "security.db")
            
            sqlite_execute(db, "CREATE TABLE IF NOT EXISTS threats (
                id INTEGER PRIMARY KEY,
                type TEXT,
                source TEXT,
                priority INTEGER,
                timestamp DATETIME,
                details TEXT
            )")
            
            sqlite_execute(db, "CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY,
                name TEXT,
                status TEXT,
                priority INTEGER,
                start_time DATETIME,
                end_time DATETIME
            )")
            
            ? "تم الاتصال بقاعدة البيانات"
        catch 
            ? "فشل الاتصال بقاعدة البيانات: " + cCatchError
        }
    
    func startSystem
        isRunning = true
        this.logMessage("بدء المراقبة الأمنية")
        uiLabelSetText(statusLabel, "🟢 المراقبة قيد التشغيل")
        
        # تحديث حالة الأزرار
        uiControlDisable(startButton)
        uiControlEnable(stopButton)
        uiControlEnable(scanButton)
        
        # إضافة مهام تجريبية
        this.addTask("network_scan", "192.168.1.0/24")
        this.addTask("port_scan", "192.168.1.1")
        this.addTask("log_analysis", "/var/log/auth.log")
        
        this.logMessage("تم إضافة المهام الأولية")
        this.simulateSecurityWork()
    
    func quickScan
        this.logMessage("بدء المسح الفوري...")
        this.addTask("port_scan", "127.0.0.1")
        this.logMessage("تم إضافة مهمة المسح الفوري")
        this.simulateQuickScan()
    
    func stopSystem
        isRunning = false
        this.logMessage("تم إيقاف النظام")
        uiLabelSetText(statusLabel, "🔴 النظام متوقف")
        
        # تحديث حالة الأزرار
        uiControlEnable(startButton)
        uiControlDisable(stopButton)
        uiControlDisable(scanButton)
        
        this.generateReport()
    
    func addTask taskType, target
        taskName = taskType + ": " + target
        priority = 5
        
        try {
            sqlite_execute(db,
                "INSERT INTO tasks (name, status, priority, start_time) 
                 VALUES ('" + taskName + "', 'معلقة', " + priority + ", datetime('now'))")
            
            # تحديث واجهة المهام
            currentTasks = uiMultilineEntryText(taskList)
            if currentTasks = "لا توجد مهام حالياً"
                currentTasks = ""
            ok
            newTasks = currentTasks + "✓ " + taskName + " - معلقة" + nl
            uiMultilineEntrySetText(taskList, newTasks)
            
            this.logMessage("تم إضافة مهمة: " + taskName)
        catch
            this.logMessage("خطأ في إضافة المهمة: " + cCatchError)
        }
    
    func simulateSecurityWork
        this.logMessage("مسح الشبكة المحلية...")
        this.logMessage("تم اكتشاف 3 أجهزة في الشبكة")
        
        this.logMessage("فحص المنافذ المفتوحة...")
        this.logMessage("تم العثور على منفذ 80 مفتوح على 192.168.1.1")
        this.logMessage("تم العثور على منفذ 22 مفتوح على 192.168.1.1")
        
        # إضافة تهديد وهمي
        this.addThreat("منفذ خطير مفتوح", "192.168.1.1:21", 8)
        
        this.logMessage("تحليل السجلات...")
        this.logMessage("تم العثور على محاولات دخول مشبوهة")
        
        # إضافة تهديد آخر
        this.addThreat("محاولات دخول مشبوهة", "/var/log/auth.log", 6)
    
    func simulateQuickScan
        this.logMessage("فحص المنافذ الشائعة...")
        this.logMessage("فحص الثغرات المعروفة...")
        this.logMessage("تم الانتهاء من المسح السريع - لا توجد تهديدات")
    
    func addThreat threatType, source, priority
        try {
            details = "تفاصيل التهديد: " + threatType + " من " + source
            encryptedDetails = Encrypt(details, "security_key")
            
            sqlite_execute(db, 
                "INSERT INTO threats (type, source, priority, timestamp, details) 
                 VALUES ('" + threatType + "', '" + source + "', " + priority + ", datetime('now'), '" + encryptedDetails + "')")
            
            # تحديث واجهة التهديدات
            currentThreats = uiMultilineEntryText(threatTable)
            if currentThreats = "لا توجد تهديدات مكتشفة"
                currentThreats = ""
            ok
            newThreats = currentThreats + "⚠️ [" + priority + "] " + threatType + " - " + source + " - " + time() + nl
            uiMultilineEntrySetText(threatTable, newThreats)
            
            this.logMessage("تم اكتشاف تهديد: " + threatType)
        catch
            this.logMessage("خطأ في تسجيل التهديد: " + cCatchError)
        }
    
    func generateReport
        report = "تقرير المراقبة الأمنية - " + date() + nl
        report += "================================" + nl + nl
        
        report += "المهام المضافة:" + nl
        try {
            tasks = sqlite_execute(db, "SELECT * FROM tasks")
            for task in tasks
                report += " - " + task['name'] + " (" + task['status'] + ")" + nl
            next
        catch
            report += "خطأ في قراءة المهام" + nl
        }
        
        report += nl + "التهديدات المكتشفة:" + nl
        try {
            threats = sqlite_execute(db, "SELECT * FROM threats ORDER BY priority DESC")
            for threat in threats
                try {
                    details = Decrypt(threat['details'], "security_key")
                catch
                    details = threat['details']
                }
                report += " - [" + threat['priority'] + "] " + threat['type'] + 
                         " (" + threat['source'] + ")" + nl
                report += "   التفاصيل: " + details + nl
            next
        catch
            report += "خطأ في قراءة التهديدات" + nl
        }
        
        report += nl + "انتهى التقرير" + nl
        
        # عرض التقرير
        uiMultilineEntrySetText(reportArea, report)
        this.logMessage("تم إنشاء التقرير")
    
    func exportReport
        report = uiMultilineEntryText(reportArea)
        filename = "security_report_" + date() + ".txt"
        try {
            file = fopen(filename, "w")
            fwrite(file, report)
            fclose(file)
            this.logMessage("تم تصدير التقرير إلى: " + filename)
        catch
            this.logMessage("فشل تصدير التقرير: " + cCatchError)
        }
    
    func logMessage msg
        if reportArea != NULL
            uiMultilineEntryAppend(reportArea, "ℹ️ " + msg + nl)
        ok
        ? msg
    
    func onClosing
        this.stopSystem()
        if db != null
            sqlite_close(db)
        ok
        uiQuit()
end

# دوال مساعدة لواجهة المستخدم
func exportReportClicked
    oGuardianEye.exportReport()

func startSystemClicked
    oGuardianEye.startSystem()

func stopSystemClicked
    oGuardianEye.stopSystem()

func quickScanClicked
    oGuardianEye.quickScan()

func onClosingClicked
    oGuardianEye.onClosing()
