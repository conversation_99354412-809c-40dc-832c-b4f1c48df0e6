load "libui.ring"
load "RingThreadPro.ring"
load "openssllib.ring"
load "sqlitelib.ring"
load "subprocess.ring"
load "ringregex.ring"
load "stdlib.ring"

if ismainsourcefile()
	// بدء النظام
	oGuardianEye = new GuardianEyeSystem()
	oGuardianEye.init()
	uiMain()
end	

class GuardianEyeSystem
    // إعدادات النظام
    poolSize = 5
    db = null
    isRunning = false
    
    // عناصر واجهة المستخدم
    mainwin = NULL
    tabs = NULL
    taskList = NULL
    threatTable = NULL
    reportArea = NULL
    statusLabel = NULL
    startButton = NULL
    stopButton = NULL
    scanButton = NULL
    
    // تهيئة النظام
    func init
        // إنشاء واجهة المستخدم
        this.createGUI()
        
        // الاتصال بقاعدة البيانات
        this.connectDB()
        
        // بدء نظام الخيوط
        this.startThreadSystem()
        
        this.logMessage("تم تهيئة نظام GuardianEye بنجاح")
    
    func createGUI
        // النافذة الرئيسية
        this.mainwin = uiNewWindow("🛡️ نظام GuardianEye الأمني", 1000, 700, 1)
        uiWindowSetMargined(this.mainwin, 1)
        
        // التخطيط الرئيسي
        mainBox = uiNewVerticalBox()
        uiBoxSetPadded(mainBox, 1)
        
        // الألسنة
        this.tabs = uiNewTab()
        uiTabSetMargined(this.tabs, 0, 1)
        
        // لسان المهام
        taskTab = uiNewVerticalBox()
        uiBoxSetPadded(taskTab, 1)
        
        taskGroup = uiNewGroup("📋 المهام النشطة")
        uiGroupSetMargined(taskGroup, 1)
        taskGroupBox = uiNewVerticalBox()
        uiBoxSetPadded(taskGroupBox, 1)
        
        this.taskList = uiNewTable()
        uiTableAppendTextColumn(this.taskList, "المهمة", 0)
        uiTableAppendTextColumn(this.taskList, "الحالة", 1)
        uiTableAppendTextColumn(this.taskList, "العامل", 2)
        uiTableAppendTextColumn(this.taskList, "الأولوية", 3)
        uiTableSetModel(this.taskList, uiNewTableModel(0, 4))
        
        uiBoxAppend(taskGroupBox, this.taskList, 1)
        uiGroupSetChild(taskGroup, taskGroupBox)
        uiBoxAppend(taskTab, taskGroup, 1)
        
        uiTabAppend(this.tabs, "المهام", taskTab)
        
        // لسان التهديدات
        threatTab = uiNewVerticalBox()
        uiBoxSetPadded(threatTab, 1)
        
        threatGroup = uiNewGroup("⚠️ التهديدات المكتشفة")
        uiGroupSetMargined(threatGroup, 1)
        threatGroupBox = uiNewVerticalBox()
        uiBoxSetPadded(threatGroupBox, 1)
        
        this.threatTable = uiNewTable()
        uiTableAppendTextColumn(this.threatTable, "نوع التهديد", 0)
        uiTableAppendTextColumn(this.threatTable, "المصدر", 1)
        uiTableAppendTextColumn(this.threatTable, "الأولوية", 2)
        uiTableAppendTextColumn(this.threatTable, "الوقت", 3)
        uiTableSetModel(this.threatTable, uiNewTableModel(0, 4))
        
        uiBoxAppend(threatGroupBox, this.threatTable, 1)
        uiGroupSetChild(threatGroup, threatGroupBox)
        uiBoxAppend(threatTab, threatGroup, 1)
        
        uiTabAppend(this.tabs, "التهديدات", threatTab)
        
        // لسان التقارير
        reportTab = uiNewVerticalBox()
        uiBoxSetPadded(reportTab, 1)
        
        reportGroup = uiNewGroup("📊 التقارير")
        uiGroupSetMargined(reportGroup, 1)
        reportGroupBox = uiNewVerticalBox()
        uiBoxSetPadded(reportGroupBox, 1)
        
        this.reportArea = uiNewMultilineEntry()
        uiMultilineEntrySetReadOnly(this.reportArea, 1)
        uiBoxAppend(reportGroupBox, this.reportArea, 1)
        
        exportBtn = uiNewButton("💾 تصدير التقرير")
        uiButtonOnClicked(exportBtn, "exportReportClicked()")
        uiBoxAppend(reportGroupBox, exportBtn, 0)
        
        uiGroupSetChild(reportGroup, reportGroupBox)
        uiBoxAppend(reportTab, reportGroup, 1)
        
        uiTabAppend(this.tabs, "التقارير", reportTab)
        
        // لوحة التحكم
        controlPanel = uiNewHorizontalBox()
        uiBoxSetPadded(controlPanel, 1)
        
        this.startButton = uiNewButton("▶️ بدء المراقبة")
        uiButtonOnClicked(this.startButton, "startSystemClicked()")

        this.stopButton = uiNewButton("⏹️ إيقاف النظام")
        uiButtonOnClicked(this.stopButton, "stopSystemClicked()")
        uiControlDisable(this.stopButton)

        this.scanButton = uiNewButton("🔍 مسح فوري")
        uiButtonOnClicked(this.scanButton, "quickScanClicked()")
        uiControlDisable(this.scanButton)
        
        uiBoxAppend(controlPanel, this.startButton, 1)
        uiBoxAppend(controlPanel, this.stopButton, 1)
        uiBoxAppend(controlPanel, this.scanButton, 1)
        
        // حالة النظام
        statusPanel = uiNewHorizontalBox()
        uiBoxSetPadded(statusPanel, 1)
        
        this.statusLabel = uiNewLabel("⭕ النظام متوقف")
        uiBoxAppend(statusPanel, this.statusLabel, 1)
        
        // تجميع الواجهة
        uiBoxAppend(mainBox, this.tabs, 1)
        uiBoxAppend(mainBox, controlPanel, 0)
        uiBoxAppend(mainBox, statusPanel, 0)
        
        uiWindowSetChild(this.mainwin, mainBox)
        uiControlShow(this.mainwin)
        uiWindowOnClosing(this.mainwin, "onClosingClicked()")
    
    func connectDB
        try{
            this.db = sqlite_init()
            sqlite_open(this.db, "security.db")

            sqlite_execute(this.db, "CREATE TABLE IF NOT EXISTS threats (
                id INTEGER PRIMARY KEY,
                type TEXT,
                source TEXT,
                priority INTEGER,
                timestamp DATETIME,
                details TEXT
            )")

            sqlite_execute(this.db, "CREATE TABLE IF NOT EXISTS tasks (
                id INTEGER PRIMARY KEY,
                name TEXT,
                status TEXT,
                worker TEXT,
                priority INTEGER,
                start_time DATETIME,
                end_time DATETIME
            )")

            sqlite_execute(this.db, "CREATE TABLE IF NOT EXISTS devices (
                ip TEXT PRIMARY KEY,
                scan_time DATETIME
            )")

            sqlite_execute(this.db, "CREATE TABLE IF NOT EXISTS open_ports (
                target TEXT,
                port TEXT,
                scan_time DATETIME
            )")

            sqlite_execute(this.db, "CREATE TABLE IF NOT EXISTS suspicious_logs (
                logfile TEXT,
                entries TEXT,
                scan_time DATETIME
            )")

            sqlite_execute(this.db, "CREATE TABLE IF NOT EXISTS vulnerabilities (
                target TEXT,
                details TEXT,
                scan_time DATETIME
            )")

            this.logMessage("تم الاتصال بقاعدة البيانات")
        catch
            this.logError("فشل الاتصال بقاعدة البيانات: " + cCatchError)
        }
    func startThreadSystem
        this.threadMgr = new ThreadManager(this.poolSize)
        this.taskMutex = this.threadMgr.createMutex(1)
        this.threatMutex = this.threadMgr.createMutex(1)
        this.newTaskEvent = this.threadMgr.createCondition()
        
        for i = 1 to this.poolSize{
            this.threadMgr.setThreadName(i, "حارس-"+i)
            this.threadMgr.createThread(i, "this.securityWorker("+i+")")
        }
    
    func securityWorker workerId
        workerName = this.threadMgr.getThreadName(workerId)
        this.logMessage(workerName + " بدأ العمل")
        
        while this.isRunning
            this.threadMgr.lockMutex(this.taskMutex)
            
            while this.getPendingTaskCount() = 0 and this.isRunning
                this.threadMgr.waitCondition(this.newTaskEvent, this.taskMutex)
            end
            
            if not this.isRunning
                this.threadMgr.unlockMutex(this.taskMutex)
                exit
            ok
            
            task = this.getNextTask()
            this.threadMgr.unlockMutex(this.taskMutex)
            
            if task = null
                continue
            ok
            
            try{
                this.updateTaskStatus(task.id, "قيد المعالجة", workerName)
                result = this.executeSecurityTask(task)
                threats = this.analyzeResults(result, task)
                
                for threat in threats
                    this.logThreat(threat, task.target, workerName)
                next
                
                this.updateTaskStatus(task.id, "مكتمل", workerName)
            catch
                this.logError("خطأ في المهمة " + task.id + ": " + cCatchError)
                this.updateTaskStatus(task.id, "فشل", workerName)
            }
        end
    
    func executeSecurityTask task
        switch task.type
        on "network_scan"
            return this.scanNetwork(task.target)
        on "port_scan"
            return this.scanPorts(task.target)
        on "log_analysis"
            return this.analyzeLogs(task.logfile)
        on "vulnerability_scan"
            return this.scanVulnerabilities(task.target)
        end
    
    func scanNetwork subnet
        devices = []
        try {
            cmd = "ping -n 1 " + subnet
            output = system(cmd)

            // محاكاة اكتشاف الأجهزة
            add(devices, subnet)
            sqlite_execute(this.db,
                "INSERT OR IGNORE INTO devices (ip, scan_time) VALUES ('" + subnet + "', datetime('now'))")
        catch
            this.logError("خطأ في مسح الشبكة: " + cCatchError)
        }
        return devices
    
    func scanPorts target
        openPorts = []
        try {
            // محاكاة مسح المنافذ
            commonPorts = ["80", "443", "22", "21", "3389"]
            for port in commonPorts
                // محاكاة اكتشاف منفذ مفتوح
                if random(10) > 7  // 30% احتمال أن يكون المنفذ مفتوح
                    add(openPorts, port)
                    sqlite_execute(this.db,
                        "INSERT OR IGNORE INTO open_ports (target, port, scan_time) VALUES ('" +
                        target + "', '" + port + "', datetime('now'))" )
                ok
            next
        catch
            this.logError("خطأ في مسح المنافذ: " + cCatchError)
        }
        return openPorts
    
    func analyzeLogs logfile
        suspicious = ""
        try {
            // محاكاة تحليل السجلات
            patterns = ["Failed password", "Unauthorized access", "Brute force attempt", "SQL injection"]
            if fexists(logfile)
                content = read(logfile)
                for pattern in patterns
                    if substr(content, pattern) > 0
                        suspicious += "تم العثور على: " + pattern + nl
                    ok
                next

                if suspicious != ""
                    sqlite_execute(this.db,
                        "INSERT INTO suspicious_logs (logfile, entries, scan_time) VALUES ('" +
                        logfile + "', '" + suspicious + "', datetime('now'))")
                end
            else
                suspicious = "محاكاة: تم العثور على أنشطة مشبوهة في السجلات"
            ok
        catch
            this.logError("خطأ في تحليل السجلات: " + cCatchError)
        }
        return suspicious
    
    func scanVulnerabilities target
        vulnerabilities = ""
        try {
            // محاكاة مسح الثغرات
            vulnTypes = ["CVE-2021-44228", "SQL Injection", "XSS", "CSRF"]
            if random(10) > 6  // 40% احتمال وجود ثغرة
                vuln = vulnTypes[random(len(vulnTypes)) + 1]
                vulnerabilities = "تم العثور على ثغرة: " + vuln + " في " + target

                sqlite_execute(this.db,
                    "INSERT INTO vulnerabilities (target, details, scan_time) VALUES ('" +
                    target + "', '" + vulnerabilities + "', datetime('now'))")
            ok
        catch
            this.logError("خطأ في مسح الثغرات: " + cCatchError)
        }
        return vulnerabilities
    
    func analyzeResults data, task
        threats = []
        if task.type = "port_scan" and typeof(data) = "ARRAY"
            dangerousPorts = ["21", "23", "135", "445"]
            for port in data
                if find(dangerousPorts, port) > 0
                    add(threats, [
                        type= "منفذ خطير مفتوح",
                        details= "منفذ " + port + " على " + task.target,
                        priority= 8
                    ])
                ok
            next
        ok
        return threats
    
    func logThreat threat, source, worker
        encryptedDetails = Encrypt(threat.details, "security_key")

        this.threadMgr.lockMutex(this.threatMutex)
        sqlite_execute(this.db,
            "INSERT INTO threats (type, source, priority, timestamp, details)
             VALUES ('" + threat.type + "', '" + source + "', " + threat.priority + ", datetime('now'), '" + encryptedDetails + "')")

        // تحديث واجهة التهديدات
        model = uiTableModel(this.threatTable)
        row = uiTableModelRowCount(model)
        uiTableModelInsertRow(model, row)
        uiTableModelSetCellValue(model, row, 0, threat.type)
        uiTableModelSetCellValue(model, row, 1, source)
        uiTableModelSetCellValue(model, row, 2, string(threat.priority))
        uiTableModelSetCellValue(model, row, 3, time())

        this.threadMgr.unlockMutex(this.threatMutex)
    
    func addTask taskType, target
        this.threadMgr.lockMutex(this.taskMutex)
        
        priorities = [
            "network_scan"= 5,
            "port_scan"= 7,
            "log_analysis"= 6,
            "vulnerability_scan"= 9
        ]
        
        priority = priorities[taskType]
        taskName = taskType + ": " + target
        
        sqlite_execute(this.db,
            "INSERT INTO tasks (name, status, priority, start_time)
             VALUES ('" + taskName + "', 'معلقة', " + priority + ", datetime('now'))")

        result = sqlite_execute(this.db, "SELECT last_insert_rowid()")
        taskId = result[1]["last_insert_rowid()"]
        
        // تحديث واجهة المهام
        model = uiTableModel(this.taskList)
        row = uiTableModelRowCount(model)
        uiTableModelInsertRow(model, row)
        uiTableModelSetCellValue(model, row, 0, taskName)
        uiTableModelSetCellValue(model, row, 1, "معلقة")
        uiTableModelSetCellValue(model, row, 2, "")
        uiTableModelSetCellValue(model, row, 3, string(priority))
        
        // إشعار الخيوط العاملة
        this.threadMgr.signalCondition(this.newTaskEvent)
        this.threadMgr.unlockMutex(this.taskMutex)
        
        return taskId
    
    func getPendingTaskCount
        result = sqlite_execute(this.db,
            "SELECT COUNT(*) FROM tasks WHERE status = 'معلقة'")
        if len(result) > 0
            return result[1]["COUNT(*)"]
        else
            return 0
        ok
    
    func getNextTask
        result = sqlite_execute(this.db,
            "SELECT * FROM tasks
             WHERE status = 'معلقة'
             ORDER BY priority DESC LIMIT 1")

        if len(result) > 0
            taskParts = split(result[1]["name"], ":")
            target = ""
            if len(taskParts) > 1
                target = trim(taskParts[2])
            ok
            return [
                :id = result[1]["id"],
                :type = trim(taskParts[1]),
                :target = target,
                :priority = result[1]["priority"]
            ]
        end
        return null
    
    func updateTaskStatus taskId, status, worker
        sqlite_execute(this.db,
            "UPDATE tasks SET status = '" + status + "', worker = '" + worker + "', end_time = datetime('now')
             WHERE id = " + taskId)

        // تحديث واجهة المهام
        model = uiTableModel(this.taskList)
        for row = 0 to uiTableModelRowCount(model)-1
            taskName = uiTableModelCellValue(model, row, 0)
            if contains(taskName, "[" + taskId + "]")
                uiTableModelSetCellValue(model, row, 1, status)
                uiTableModelSetCellValue(model, row, 2, worker)
                exit
            ok
        next
    
    func startSystem
        this.isRunning = true
        this.logMessage("بدء المراقبة الأمنية")
        uiLabelSetText(this.statusLabel, "🟢 المراقبة قيد التشغيل")
        
        // تحديث حالة الأزرار
        uiControlDisable(this.startButton)
        uiControlEnable(this.stopButton)
        uiControlEnable(this.scanButton)
        
        // إضافة مهام أولية
        this.addTask("network_scan", "192.168.1.0/24")
        this.addTask("log_analysis", "/var/log/auth.log")
        this.addTask("vulnerability_scan", "192.168.1.1")
    
    func quickScan
        target = uiEntryText(uiNewEntry())
        if target != ""
            taskId = this.addTask("port_scan", target)
            this.logMessage("بدء المسح الفوري للهدف: " + target)
        end
    
    func stopSystem
        this.isRunning = false
        this.threadMgr.signalCondition(this.newTaskEvent)
        this.generateReport()
        this.logMessage("تم إيقاف النظام")
        uiLabelSetText(this.statusLabel, "🔴 النظام متوقف")
        
        // تحديث حالة الأزرار
        uiControlEnable(this.startButton)
        uiControlDisable(this.stopButton)
        uiControlDisable(this.scanButton)
        
        sqlite_close(this.db)
    
    func generateReport
        report = "تقرير المراقبة الأمنية - " + date() + "\n"
        report += "================================\n\n"
        
        // المهام المكتملة
        report += "المهام المكتملة:\n"
        tasks = sqlite_execute(this.db,
            "SELECT * FROM tasks WHERE status = 'مكتمل'")

        for task in tasks
            report += " - " + task['name'] + " (" + task['worker'] + ")\n"
        next

        // التهديدات المكتشفة
        report += "\nالتهديدات المكتشفة:\n"
        threats = sqlite_execute(this.db,
            "SELECT * FROM threats ORDER BY priority DESC")

        for threat in threats
            try {
                details = Decrypt(threat['details'], "security_key")
            catch
                details = threat['details']
            }
            report += " - [" + threat['priority'] + "] " + threat['type'] +
                     " (" + threat['source'] + ")\n"
            report += "   التفاصيل: " + details + "\n"
        next
        
        // عرض التقرير
        uiMultilineEntrySetText(this.reportArea, report)
    
    func exportReport
        report = uiMultilineEntryText(this.reportArea)
        filename = "security_report_" + date() + ".txt"
        file = fopen(filename, "w")
        fwrite(file, report)
        fclose(file)
        this.logMessage("تم تصدير التقرير إلى: " + filename)
    
    func logMessage msg
        uiMultilineEntryAppend(this.reportArea, "ℹ️ " + msg + "\n")
    
    func logError msg
        uiMultilineEntryAppend(this.reportArea, "❌ " + msg + "\n")
    
    func onClosing
        this.stopSystem()
        uiQuit()
end

// دوال مساعدة لواجهة المستخدم
func exportReportClicked
    oGuardianEye.exportReport()

func startSystemClicked
    oGuardianEye.startSystem()

func stopSystemClicked
    oGuardianEye.stopSystem()

func quickScanClicked
    oGuardianEye.quickScan()

func onClosingClicked
    oGuardianEye.onClosing()

