@echo off
echo تشغيل نظام GuardianEye الأمني...
echo.
echo اختر النسخة المطلوبة:
echo 1. النسخة الكاملة (مع الخيوط) - قد لا تعمل على جميع الأنظمة
echo 2. النسخة المبسطة (بدون خيوط) - مستقرة
echo 3. النسخة التجريبية البسيطة
echo.
set /p choice="اختر رقم النسخة (1-3): "

if "%choice%"=="1" (
    echo تشغيل النسخة الكاملة...
    ring main.ring
) else if "%choice%"=="2" (
    echo تشغيل النسخة المبسطة...
    ring guardian_no_threads.ring
) else if "%choice%"=="3" (
    echo تشغيل النسخة التجريبية...
    ring guardian_simple.ring
) else (
    echo خيار غير صحيح، تشغيل النسخة المبسطة...
    ring guardian_no_threads.ring
)

pause
